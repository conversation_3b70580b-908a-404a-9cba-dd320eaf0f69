# Project Overview

## Environment Setup
Execute the `source setup.sh` command to setup the Flutter environment before making any code changes. Fix any issue reported by the `setup.sh`. 

## Before you submit changes
Make sure you execute the following commands and fix all reported errors:
```
very_good test
dart format --line-length 80 lib test
dart fix --apply
flutter analyze
very_good test --coverage
```
