import 'package:flutter/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_view.dart';
import 'package:stacked/stacked.dart';

/// A view model that handles the business logic of the [SettingsView]
class SettingsViewModel extends BaseViewModel {
  /// Creates a new instance of the [SettingsViewModel]
  SettingsViewModel({FlutterSecureStorage? secureStorage})
    : _secureStorage = secureStorage ?? const FlutterSecureStorage();
  final FlutterSecureStorage _secureStorage;
  final _apiKeyController = TextEditingController();

  /// The controller for the API key text field
  TextEditingController get apiKeyController => _apiKeyController;

  /// Saves the API key to the secure storage
  Future<void> saveApiKey() async {
    await _secureStorage.write(
      key: 'llm_api_key',
      value: _apiKeyController.text,
    );
    // Optionally, show a success message to the user
  }
}
