import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_viewmodel.dart';
import 'package:stacked/stacked.dart';

/// A view that allows the user to configure the settings of the application
class SettingsView extends StackedView<SettingsViewModel> {
  /// Creates a new instance of the [SettingsView]
  const SettingsView({this.viewModel, super.key});

  /// The view model to use.
  final SettingsViewModel? viewModel;

  @override
  Widget builder(
    BuildContext context,
    SettingsViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        leading: IconButton(
          key: const Key('backButton'),
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              key: const Key('api<PERSON><PERSON><PERSON>ield'),
              controller: viewModel.apiKeyController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'LLM API Key',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              key: const Key('saveApiKeyButton'),
              onPressed: viewModel.saveApiKey,
              child: const Text('Save API Key'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  SettingsViewModel viewModelBuilder(BuildContext context) {
    return viewModel ?? SettingsViewModel();
  }
}
