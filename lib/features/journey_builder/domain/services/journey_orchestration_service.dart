import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:rxdart/rxdart.dart';

/// A service that orchestrates the journey builder.
class JourneyOrchestrationService {
  final _selectedNode = BehaviorSubject<Node?>.seeded(null);

  /// A stream of the currently selected node.
  Stream<Node?> get selectedNodeStream => _selectedNode.stream;

  /// The currently selected node.
  Node? get selectedNode => _selectedNode.value;

  /// Selects a node.
  void selectNode(Node? node) {
    _selectedNode.add(node);
  }

  /// Disposes of the service.
  Future<void> dispose() async {
    await _selectedNode.close();
  }
}
