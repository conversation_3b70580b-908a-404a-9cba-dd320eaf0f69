// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'node.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StartNode _$StartNodeFromJson(Map<String, dynamic> json) => StartNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$StartNodeToJson(StartNode instance) => <String, dynamic>{
  'id': instance.id,
  'position': const OffsetJsonConverter().toJson(instance.position),
  'runtimeType': instance.$type,
};

EndNode _$EndNodeFromJson(Map<String, dynamic> json) => EndNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$EndNodeToJson(EndNode instance) => <String, dynamic>{
  'id': instance.id,
  'position': const OffsetJsonConverter().toJson(instance.position),
  'runtimeType': instance.$type,
};

DecisionNode _$DecisionNodeFromJson(Map<String, dynamic> json) => DecisionNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$DecisionNodeToJson(DecisionNode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'position': const OffsetJsonConverter().toJson(instance.position),
      'runtimeType': instance.$type,
    };

LLMNode _$LLMNodeFromJson(Map<String, dynamic> json) => LLMNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  prompt: json['prompt'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$LLMNodeToJson(LLMNode instance) => <String, dynamic>{
  'id': instance.id,
  'position': const OffsetJsonConverter().toJson(instance.position),
  'prompt': instance.prompt,
  'runtimeType': instance.$type,
};

ToolNode _$ToolNodeFromJson(Map<String, dynamic> json) => ToolNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$ToolNodeToJson(ToolNode instance) => <String, dynamic>{
  'id': instance.id,
  'position': const OffsetJsonConverter().toJson(instance.position),
  'runtimeType': instance.$type,
};

MessageNode _$MessageNodeFromJson(Map<String, dynamic> json) => MessageNode(
  id: json['id'] as String,
  position: const OffsetJsonConverter().fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  content: json['content'] as String? ?? '',
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$MessageNodeToJson(MessageNode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'position': const OffsetJsonConverter().toJson(instance.position),
      'content': instance.content,
      'runtimeType': instance.$type,
    };
