// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'node.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
Node _$NodeFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'start':
          return StartNode.fromJson(
            json
          );
                case 'end':
          return EndNode.fromJson(
            json
          );
                case 'decision':
          return DecisionNode.fromJson(
            json
          );
                case 'llm':
          return LLMNode.fromJson(
            json
          );
                case 'tool':
          return ToolNode.fromJson(
            json
          );
                case 'message':
          return MessageNode.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'Node',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$Node {

 String get id;@OffsetJsonConverter() Offset get position;
/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$NodeCopyWith<Node> get copyWith => _$NodeCopyWithImpl<Node>(this as Node, _$identity);

  /// Serializes this Node to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Node&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position);

@override
String toString() {
  return 'Node(id: $id, position: $position)';
}


}

/// @nodoc
abstract mixin class $NodeCopyWith<$Res>  {
  factory $NodeCopyWith(Node value, $Res Function(Node) _then) = _$NodeCopyWithImpl;
@useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position
});




}
/// @nodoc
class _$NodeCopyWithImpl<$Res>
    implements $NodeCopyWith<$Res> {
  _$NodeCopyWithImpl(this._self, this._then);

  final Node _self;
  final $Res Function(Node) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? position = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,
  ));
}

}


/// Adds pattern-matching-related methods to [Node].
extension NodePatterns on Node {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( StartNode value)?  start,TResult Function( EndNode value)?  end,TResult Function( DecisionNode value)?  decision,TResult Function( LLMNode value)?  llm,TResult Function( ToolNode value)?  tool,TResult Function( MessageNode value)?  message,required TResult orElse(),}){
final _that = this;
switch (_that) {
case StartNode() when start != null:
return start(_that);case EndNode() when end != null:
return end(_that);case DecisionNode() when decision != null:
return decision(_that);case LLMNode() when llm != null:
return llm(_that);case ToolNode() when tool != null:
return tool(_that);case MessageNode() when message != null:
return message(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( StartNode value)  start,required TResult Function( EndNode value)  end,required TResult Function( DecisionNode value)  decision,required TResult Function( LLMNode value)  llm,required TResult Function( ToolNode value)  tool,required TResult Function( MessageNode value)  message,}){
final _that = this;
switch (_that) {
case StartNode():
return start(_that);case EndNode():
return end(_that);case DecisionNode():
return decision(_that);case LLMNode():
return llm(_that);case ToolNode():
return tool(_that);case MessageNode():
return message(_that);}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( StartNode value)?  start,TResult? Function( EndNode value)?  end,TResult? Function( DecisionNode value)?  decision,TResult? Function( LLMNode value)?  llm,TResult? Function( ToolNode value)?  tool,TResult? Function( MessageNode value)?  message,}){
final _that = this;
switch (_that) {
case StartNode() when start != null:
return start(_that);case EndNode() when end != null:
return end(_that);case DecisionNode() when decision != null:
return decision(_that);case LLMNode() when llm != null:
return llm(_that);case ToolNode() when tool != null:
return tool(_that);case MessageNode() when message != null:
return message(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function( String id, @OffsetJsonConverter()  Offset position)?  start,TResult Function( String id, @OffsetJsonConverter()  Offset position)?  end,TResult Function( String id, @OffsetJsonConverter()  Offset position)?  decision,TResult Function( String id, @OffsetJsonConverter()  Offset position,  String prompt)?  llm,TResult Function( String id, @OffsetJsonConverter()  Offset position)?  tool,TResult Function( String id, @OffsetJsonConverter()  Offset position,  String content)?  message,required TResult orElse(),}) {final _that = this;
switch (_that) {
case StartNode() when start != null:
return start(_that.id,_that.position);case EndNode() when end != null:
return end(_that.id,_that.position);case DecisionNode() when decision != null:
return decision(_that.id,_that.position);case LLMNode() when llm != null:
return llm(_that.id,_that.position,_that.prompt);case ToolNode() when tool != null:
return tool(_that.id,_that.position);case MessageNode() when message != null:
return message(_that.id,_that.position,_that.content);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function( String id, @OffsetJsonConverter()  Offset position)  start,required TResult Function( String id, @OffsetJsonConverter()  Offset position)  end,required TResult Function( String id, @OffsetJsonConverter()  Offset position)  decision,required TResult Function( String id, @OffsetJsonConverter()  Offset position,  String prompt)  llm,required TResult Function( String id, @OffsetJsonConverter()  Offset position)  tool,required TResult Function( String id, @OffsetJsonConverter()  Offset position,  String content)  message,}) {final _that = this;
switch (_that) {
case StartNode():
return start(_that.id,_that.position);case EndNode():
return end(_that.id,_that.position);case DecisionNode():
return decision(_that.id,_that.position);case LLMNode():
return llm(_that.id,_that.position,_that.prompt);case ToolNode():
return tool(_that.id,_that.position);case MessageNode():
return message(_that.id,_that.position,_that.content);}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function( String id, @OffsetJsonConverter()  Offset position)?  start,TResult? Function( String id, @OffsetJsonConverter()  Offset position)?  end,TResult? Function( String id, @OffsetJsonConverter()  Offset position)?  decision,TResult? Function( String id, @OffsetJsonConverter()  Offset position,  String prompt)?  llm,TResult? Function( String id, @OffsetJsonConverter()  Offset position)?  tool,TResult? Function( String id, @OffsetJsonConverter()  Offset position,  String content)?  message,}) {final _that = this;
switch (_that) {
case StartNode() when start != null:
return start(_that.id,_that.position);case EndNode() when end != null:
return end(_that.id,_that.position);case DecisionNode() when decision != null:
return decision(_that.id,_that.position);case LLMNode() when llm != null:
return llm(_that.id,_that.position,_that.prompt);case ToolNode() when tool != null:
return tool(_that.id,_that.position);case MessageNode() when message != null:
return message(_that.id,_that.position,_that.content);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class StartNode implements Node {
  const StartNode({required this.id, @OffsetJsonConverter() required this.position, final  String? $type}): $type = $type ?? 'start';
  factory StartNode.fromJson(Map<String, dynamic> json) => _$StartNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StartNodeCopyWith<StartNode> get copyWith => _$StartNodeCopyWithImpl<StartNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$StartNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StartNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position);

@override
String toString() {
  return 'Node.start(id: $id, position: $position)';
}


}

/// @nodoc
abstract mixin class $StartNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $StartNodeCopyWith(StartNode value, $Res Function(StartNode) _then) = _$StartNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position
});




}
/// @nodoc
class _$StartNodeCopyWithImpl<$Res>
    implements $StartNodeCopyWith<$Res> {
  _$StartNodeCopyWithImpl(this._self, this._then);

  final StartNode _self;
  final $Res Function(StartNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,}) {
  return _then(StartNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,
  ));
}


}

/// @nodoc
@JsonSerializable()

class EndNode implements Node {
  const EndNode({required this.id, @OffsetJsonConverter() required this.position, final  String? $type}): $type = $type ?? 'end';
  factory EndNode.fromJson(Map<String, dynamic> json) => _$EndNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EndNodeCopyWith<EndNode> get copyWith => _$EndNodeCopyWithImpl<EndNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EndNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EndNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position);

@override
String toString() {
  return 'Node.end(id: $id, position: $position)';
}


}

/// @nodoc
abstract mixin class $EndNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $EndNodeCopyWith(EndNode value, $Res Function(EndNode) _then) = _$EndNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position
});




}
/// @nodoc
class _$EndNodeCopyWithImpl<$Res>
    implements $EndNodeCopyWith<$Res> {
  _$EndNodeCopyWithImpl(this._self, this._then);

  final EndNode _self;
  final $Res Function(EndNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,}) {
  return _then(EndNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,
  ));
}


}

/// @nodoc
@JsonSerializable()

class DecisionNode implements Node {
  const DecisionNode({required this.id, @OffsetJsonConverter() required this.position, final  String? $type}): $type = $type ?? 'decision';
  factory DecisionNode.fromJson(Map<String, dynamic> json) => _$DecisionNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DecisionNodeCopyWith<DecisionNode> get copyWith => _$DecisionNodeCopyWithImpl<DecisionNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DecisionNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DecisionNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position);

@override
String toString() {
  return 'Node.decision(id: $id, position: $position)';
}


}

/// @nodoc
abstract mixin class $DecisionNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $DecisionNodeCopyWith(DecisionNode value, $Res Function(DecisionNode) _then) = _$DecisionNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position
});




}
/// @nodoc
class _$DecisionNodeCopyWithImpl<$Res>
    implements $DecisionNodeCopyWith<$Res> {
  _$DecisionNodeCopyWithImpl(this._self, this._then);

  final DecisionNode _self;
  final $Res Function(DecisionNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,}) {
  return _then(DecisionNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,
  ));
}


}

/// @nodoc
@JsonSerializable()

class LLMNode implements Node {
  const LLMNode({required this.id, @OffsetJsonConverter() required this.position, this.prompt = '', final  String? $type}): $type = $type ?? 'llm';
  factory LLMNode.fromJson(Map<String, dynamic> json) => _$LLMNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;
@JsonKey() final  String prompt;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LLMNodeCopyWith<LLMNode> get copyWith => _$LLMNodeCopyWithImpl<LLMNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LLMNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LLMNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position)&&(identical(other.prompt, prompt) || other.prompt == prompt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position,prompt);

@override
String toString() {
  return 'Node.llm(id: $id, position: $position, prompt: $prompt)';
}


}

/// @nodoc
abstract mixin class $LLMNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $LLMNodeCopyWith(LLMNode value, $Res Function(LLMNode) _then) = _$LLMNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position, String prompt
});




}
/// @nodoc
class _$LLMNodeCopyWithImpl<$Res>
    implements $LLMNodeCopyWith<$Res> {
  _$LLMNodeCopyWithImpl(this._self, this._then);

  final LLMNode _self;
  final $Res Function(LLMNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,Object? prompt = null,}) {
  return _then(LLMNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,prompt: null == prompt ? _self.prompt : prompt // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
@JsonSerializable()

class ToolNode implements Node {
  const ToolNode({required this.id, @OffsetJsonConverter() required this.position, final  String? $type}): $type = $type ?? 'tool';
  factory ToolNode.fromJson(Map<String, dynamic> json) => _$ToolNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ToolNodeCopyWith<ToolNode> get copyWith => _$ToolNodeCopyWithImpl<ToolNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ToolNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ToolNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position);

@override
String toString() {
  return 'Node.tool(id: $id, position: $position)';
}


}

/// @nodoc
abstract mixin class $ToolNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $ToolNodeCopyWith(ToolNode value, $Res Function(ToolNode) _then) = _$ToolNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position
});




}
/// @nodoc
class _$ToolNodeCopyWithImpl<$Res>
    implements $ToolNodeCopyWith<$Res> {
  _$ToolNodeCopyWithImpl(this._self, this._then);

  final ToolNode _self;
  final $Res Function(ToolNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,}) {
  return _then(ToolNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,
  ));
}


}

/// @nodoc
@JsonSerializable()

class MessageNode implements Node {
  const MessageNode({required this.id, @OffsetJsonConverter() required this.position, this.content = '', final  String? $type}): $type = $type ?? 'message';
  factory MessageNode.fromJson(Map<String, dynamic> json) => _$MessageNodeFromJson(json);

@override final  String id;
@override@OffsetJsonConverter() final  Offset position;
@JsonKey() final  String content;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageNodeCopyWith<MessageNode> get copyWith => _$MessageNodeCopyWithImpl<MessageNode>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageNodeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageNode&&(identical(other.id, id) || other.id == id)&&(identical(other.position, position) || other.position == position)&&(identical(other.content, content) || other.content == content));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,position,content);

@override
String toString() {
  return 'Node.message(id: $id, position: $position, content: $content)';
}


}

/// @nodoc
abstract mixin class $MessageNodeCopyWith<$Res> implements $NodeCopyWith<$Res> {
  factory $MessageNodeCopyWith(MessageNode value, $Res Function(MessageNode) _then) = _$MessageNodeCopyWithImpl;
@override @useResult
$Res call({
 String id,@OffsetJsonConverter() Offset position, String content
});




}
/// @nodoc
class _$MessageNodeCopyWithImpl<$Res>
    implements $MessageNodeCopyWith<$Res> {
  _$MessageNodeCopyWithImpl(this._self, this._then);

  final MessageNode _self;
  final $Res Function(MessageNode) _then;

/// Create a copy of Node
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? position = null,Object? content = null,}) {
  return _then(MessageNode(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,position: null == position ? _self.position : position // ignore: cast_nullable_to_non_nullable
as Offset,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
