import 'dart:ui';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/offset_json_converter.dart';

part 'node.freezed.dart';
part 'node.g.dart';

/// Represents a node in the journey.
@freezed
sealed class Node with _$Node {
  /// Creates a new instance of a [StartNode].
  const factory Node.start({
    required String id,
    @OffsetJsonConverter() required Offset position,
  }) = StartNode;

  /// Creates a new instance of an [EndNode].
  const factory Node.end({
    required String id,
    @OffsetJsonConverter() required Offset position,
  }) = EndNode;

  /// Creates a new instance of a [DecisionNode].
  const factory Node.decision({
    required String id,
    @OffsetJsonConverter() required Offset position,
  }) = DecisionNode;

  /// Creates a new instance of an [LLMNode].
  const factory Node.llm({
    required String id,
    @OffsetJsonConverter() required Offset position,
    @Default('') String prompt,
  }) = LLMNode;

  /// Creates a new instance of a [ToolNode].
  const factory Node.tool({
    required String id,
    @OffsetJsonConverter() required Offset position,
  }) = ToolNode;

  /// Creates a new instance of a [MessageNode].
  const factory Node.message({
    required String id,
    @OffsetJsonConverter() required Offset position,
    @Default('') String content,
  }) = MessageNode;

  /// Creates a new instance of a [Node] from a JSON object.
  factory Node.fromJson(Map<String, dynamic> json) => _$NodeFromJson(json);
}
