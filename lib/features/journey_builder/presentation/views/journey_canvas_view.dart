import 'package:fl_nodes/fl_nodes.dart' as fln;
import 'package:flutter/material.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/app/app.router.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart'
    as app;
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/view_models/journey_canvas_view_model.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/node_palette.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/properties_panel.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';

/// The view for the journey canvas.
class JourneyCanvasView extends StatefulWidget {
  /// The constructor for the journey canvas view.
  const JourneyCanvasView({super.key});

  @override
  State<JourneyCanvasView> createState() => _JourneyCanvasViewState();
}

class _JourneyCanvasViewState extends State<JourneyCanvasView> {
  final _nodeEditorController = fln.FlNodeEditorController();

  Map<String, dynamic> _nodeToJson(app.Node node) {
    return {
      'id': node.id,
      'position': {
        'x': node.position.dx,
        'y': node.position.dy,
      },
      'type': node.map(
        start: (_) => 'start',
        end: (_) => 'end',
        decision: (_) => 'decision',
        llm: (_) => 'llm',
        tool: (_) => 'tool',
        message: (_) => 'message',
      ),
      'data': node.map<Map<String, dynamic>>(
        start: (_) => <String, dynamic>{},
        end: (_) => <String, dynamic>{},
        decision: (_) => <String, dynamic>{},
        llm: (llm) => <String, dynamic>{'prompt': llm.prompt},
        tool: (_) => <String, dynamic>{},
        message: (message) => <String, dynamic>{'content': message.content},
      ),
    };
  }

  Map<String, dynamic> _connectionToJson(Connection connection) {
    return {
      'id': connection.id,
      'fromNodeId': connection.fromNodeId,
      'fromPortId': connection.fromPortId,
      'toNodeId': connection.toNodeId,
      'toPortId': connection.toPortId,
    };
  }

  void _loadJourneyIntoController(Journey? journey, BuildContext context) {
    if (journey == null) {
      return;
    }

    final projectData = {
      'nodes': journey.nodes.map(_nodeToJson).toList(),
      'connections': journey.connections.map(_connectionToJson).toList(),
    };

    _nodeEditorController.project.load(data: projectData, context: context);
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<JourneyCanvasViewModel>.reactive(
      viewModelBuilder: JourneyCanvasViewModel.new,
      onViewModelReady: (viewModel) => viewModel.initialise(),
      builder: (context, viewModel, child) {
        _loadJourneyIntoController(viewModel.journey, context);
        return Scaffold(
          appBar: AppBar(
            title: const Text('Journey Builder'),
            actions: [
              IconButton(
                key: const Key('settingsButton'),
                icon: const Icon(Icons.settings),
                onPressed: () {
                  locator<NavigationService>().navigateTo<String>(
                    Routes.settingsView,
                  );
                },
              ),
            ],
          ),
          body: Row(
            children: [
              const NodePalette(),
              Expanded(
                child: DragTarget<NodeType>(
                  onAcceptWithDetails: (details) {
                    final offset = details.offset;
                    viewModel.addNode(details.data, offset);
                  },
                  builder: (context, candidateData, rejectedData) {
                    return viewModel.isBusy
                        ? const Center(child: CircularProgressIndicator())
                        : fln.FlNodeEditorWidget(
                            controller: _nodeEditorController,
                            overlay: () => [],
                          );
                  },
                ),
              ),
              const PropertiesPanel(),
            ],
          ),
        );
      },
    );
  }
}
