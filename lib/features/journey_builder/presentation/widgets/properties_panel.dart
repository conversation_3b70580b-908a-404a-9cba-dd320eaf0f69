import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/viewmodels/properties_panel_viewmodel.dart';
import 'package:stacked/stacked.dart';

/// A widget that displays the properties of the selected node.
class PropertiesPanel extends StatelessWidget {
  /// Creates a new instance of the [PropertiesPanel].
  const PropertiesPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<PropertiesPanelViewModel>.reactive(
      viewModelBuilder: PropertiesPanelViewModel.new,
      builder: (context, viewModel, child) {
        final selectedNode = viewModel.selectedNode;

        return SizedBox(
          width: 250,
          child: Card(
            child: selectedNode == null
                ? const Center(
                    child: Text('No node selected'),
                  )
                : Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Properties',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 16),
                        Text('ID: ${selectedNode.id}'),
                        const SizedBox(height: 8),
                        Text(
                          'Type: ${selectedNode.map(
                            start: (_) => 'Start',
                            end: (_) => 'End',
                            decision: (_) => 'Decision',
                            llm: (_) => 'LLM',
                            tool: (_) => 'Tool',
                            message: (_) => 'Message',
                          )}',
                        ),
                        const SizedBox(height: 16),
                        if (selectedNode is LLMNode)
                          TextFormField(
                            initialValue: selectedNode.prompt,
                            decoration: const InputDecoration(
                              labelText: 'Prompt',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              viewModel.updateNode(
                                selectedNode.copyWith(prompt: value),
                              );
                            },
                          ),
                        if (selectedNode is MessageNode)
                          TextFormField(
                            initialValue: selectedNode.content,
                            decoration: const InputDecoration(
                              labelText: 'Content',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) {
                              viewModel.updateNode(
                                selectedNode.copyWith(content: value),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
          ),
        );
      },
    );
  }
}
