import 'package:flutter/material.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart'
    as app;
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:stacked/stacked.dart';
import 'package:uuid/uuid.dart';

/// The view model for the journey canvas.
class JourneyCanvasViewModel extends BaseViewModel {
  /// The constructor for the journey canvas view model.
  JourneyCanvasViewModel();

  final JourneyRepository _journeyRepository = locator<JourneyRepository>();
  final _uuid = const Uuid();

  Journey? _journey;

  /// The current journey.
  Journey? get journey => _journey;

  /// Initialises the view model.
  Future<void> initialise() async {
    setBusy(true);
    _journey = await _journeyRepository.getJourney();
    if (_journey == null) {
      _journey = Journey(id: _uuid.v4(), nodes: [], connections: []);
      await _journeyRepository.saveJourney(_journey!);
    }
    setBusy(false);
  }

  /// Adds a new node to the journey.
  void addNode(NodeType type, Offset position) {
    final newNode = switch (type) {
      NodeType.start => app.StartNode(id: _uuid.v4(), position: position),
      NodeType.end => app.EndNode(id: _uuid.v4(), position: position),
      NodeType.decision => app.DecisionNode(id: _uuid.v4(), position: position),
      NodeType.llm => app.LLMNode(id: _uuid.v4(), position: position),
      NodeType.tool => app.ToolNode(id: _uuid.v4(), position: position),
      NodeType.message => app.MessageNode(id: _uuid.v4(), position: position),
    };
    final newNodes = [..._journey!.nodes, newNode];
    _journey = _journey!.copyWith(nodes: newNodes);
    _journeyRepository.saveJourney(_journey!);
    notifyListeners();
  }
}
