import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/journey_orchestration_service.dart';
import 'package:stacked/stacked.dart';

/// The view model for the properties panel.
class PropertiesPanelViewModel extends BaseViewModel {
  /// Creates a new instance of the [PropertiesPanelViewModel].
  PropertiesPanelViewModel() {
    _journeyOrchestrationService.selectedNodeStream.listen((node) {
      rebuildUi();
    });
  }
  final JourneyOrchestrationService _journeyOrchestrationService =
      locator<JourneyOrchestrationService>();
  final JourneyRepository _journeyRepository = locator<JourneyRepository>();

  /// The currently selected node.
  Node? get selectedNode => _journeyOrchestrationService.selectedNode;

  /// Updates a node in the journey.
  Future<void> updateNode(Node updatedNode) async {
    final journey = await _journeyRepository.getJourney();
    if (journey == null) {
      return;
    }

    final newNodes = journey.nodes.map((node) {
      return node.id == updatedNode.id ? updatedNode : node;
    }).toList();

    final updatedJourney = journey.copyWith(nodes: newNodes);
    await _journeyRepository.saveJourney(updatedJourney);
    _journeyOrchestrationService.selectNode(updatedNode);
  }
}
