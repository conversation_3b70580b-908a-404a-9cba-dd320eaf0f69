// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// StackedLocatorGenerator
// **************************************************************************

// ignore_for_file: public_member_api_docs, implementation_imports, depend_on_referenced_packages

import 'package:stacked_services/src/dialog/dialog_service.dart';
import 'package:stacked_services/src/navigation/navigation_service.dart';
import 'package:stacked_shared/stacked_shared.dart';

import '../features/journey_builder/data/repositories/journey_repository_provider.dart';
import '../features/journey_builder/domain/repositories/journey_repository.dart';
import '../features/journey_builder/domain/services/journey_orchestration_service.dart';
import '../features/journey_builder/domain/services/placeholder_engine_service.dart';

final locator = StackedLocator.instance;

Future<void> setupLocator({
  String? environment,
  EnvironmentFilter? environmentFilter,
}) async {
  // Register environments
  locator.registerEnvironment(
    environment: environment,
    environmentFilter: environmentFilter,
  );

  // Register dependencies
  locator.registerLazySingleton(() => NavigationService());
  locator.registerLazySingleton(() => DialogService());
  locator.registerLazySingleton(() => PlaceholderEngineService());
  locator.registerLazySingleton<JourneyRepository>(
    () => JourneyRepositoryImpl(),
  );
  locator.registerLazySingleton(() => JourneyOrchestrationService());
}
