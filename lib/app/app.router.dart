// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// StackedNavigatorGenerator
// **************************************************************************

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter/material.dart' as _i4;
import 'package:flutter/material.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/views/journey_canvas_view.dart'
    as _i2;
import 'package:parlant_flutter/features/settings/presentation/settings_view.dart'
    as _i3;
import 'package:parlant_flutter/features/settings/presentation/settings_viewmodel.dart'
    as _i5;
import 'package:stacked/stacked.dart' as _i1;
import 'package:stacked_services/stacked_services.dart' as _i6;

class Routes {
  static const journeyCanvasView = '/';

  static const settingsView = '/settings-view';

  static const all = <String>{journeyCanvasView, settingsView};
}

class StackedRouter extends _i1.RouterBase {
  final _routes = <_i1.RouteDef>[
    _i1.RouteDef(Routes.journeyCanvasView, page: _i2.JourneyCanvasView),
    _i1.RouteDef(Routes.settingsView, page: _i3.SettingsView),
  ];

  final _pagesMap = <Type, _i1.StackedRouteFactory>{
    _i2.JourneyCanvasView: (data) {
      final args = data.getArgs<JourneyCanvasViewArguments>(
        orElse: () => const JourneyCanvasViewArguments(),
      );
      return _i4.MaterialPageRoute<dynamic>(
        builder: (context) => _i2.JourneyCanvasView(key: args.key),
        settings: data,
      );
    },
    _i3.SettingsView: (data) {
      final args = data.getArgs<SettingsViewArguments>(
        orElse: () => const SettingsViewArguments(),
      );
      return _i4.MaterialPageRoute<dynamic>(
        builder: (context) =>
            _i3.SettingsView(viewModel: args.viewModel, key: args.key),
        settings: data,
      );
    },
  };

  @override
  List<_i1.RouteDef> get routes => _routes;

  @override
  Map<Type, _i1.StackedRouteFactory> get pagesMap => _pagesMap;
}

class JourneyCanvasViewArguments {
  const JourneyCanvasViewArguments({this.key});

  final _i4.Key? key;

  @override
  String toString() {
    return '{"key": "$key"}';
  }

  @override
  bool operator ==(covariant JourneyCanvasViewArguments other) {
    if (identical(this, other)) return true;
    return other.key == key;
  }

  @override
  int get hashCode {
    return key.hashCode;
  }
}

class SettingsViewArguments {
  const SettingsViewArguments({this.viewModel, this.key});

  final _i5.SettingsViewModel? viewModel;

  final _i4.Key? key;

  @override
  String toString() {
    return '{"viewModel": "$viewModel", "key": "$key"}';
  }

  @override
  bool operator ==(covariant SettingsViewArguments other) {
    if (identical(this, other)) return true;
    return other.viewModel == viewModel && other.key == key;
  }

  @override
  int get hashCode {
    return viewModel.hashCode ^ key.hashCode;
  }
}

extension NavigatorStateExtension on _i6.NavigationService {
  Future<dynamic> navigateToJourneyCanvasView({
    _i4.Key? key,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
    transition,
  }) async {
    return navigateTo<dynamic>(
      Routes.journeyCanvasView,
      arguments: JourneyCanvasViewArguments(key: key),
      id: routerId,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
      transition: transition,
    );
  }

  Future<dynamic> navigateToSettingsView({
    _i5.SettingsViewModel? viewModel,
    _i4.Key? key,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
    transition,
  }) async {
    return navigateTo<dynamic>(
      Routes.settingsView,
      arguments: SettingsViewArguments(viewModel: viewModel, key: key),
      id: routerId,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
      transition: transition,
    );
  }

  Future<dynamic> replaceWithJourneyCanvasView({
    _i4.Key? key,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
    transition,
  }) async {
    return replaceWith<dynamic>(
      Routes.journeyCanvasView,
      arguments: JourneyCanvasViewArguments(key: key),
      id: routerId,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
      transition: transition,
    );
  }

  Future<dynamic> replaceWithSettingsView({
    _i5.SettingsViewModel? viewModel,
    _i4.Key? key,
    int? routerId,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
    transition,
  }) async {
    return replaceWith<dynamic>(
      Routes.settingsView,
      arguments: SettingsViewArguments(viewModel: viewModel, key: key),
      id: routerId,
      preventDuplicates: preventDuplicates,
      parameters: parameters,
      transition: transition,
    );
  }
}
