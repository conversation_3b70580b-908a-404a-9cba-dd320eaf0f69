import 'package:parlant_flutter/features/journey_builder/data/repositories/journey_repository_provider.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/journey_orchestration_service.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/placeholder_engine_service.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/views/journey_canvas_view.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_view.dart';
import 'package:stacked/stacked_annotations.dart';
import 'package:stacked_services/stacked_services.dart';

@StackedApp(
  routes: [
    MaterialRoute(page: JourneyCanvasView, initial: true),
    MaterialRoute(page: SettingsView),
  ],
  dependencies: [
    LazySingleton<NavigationService>(classType: NavigationService),
    LazySingleton<DialogService>(classType: DialogService),
    LazySingleton<PlaceholderEngineService>(
      classType: PlaceholderEngineService,
    ),
    LazySingleton<JourneyRepository>(
      classType: JourneyRepositoryImpl,
    ),
    LazySingleton<JourneyOrchestrationService>(
      classType: JourneyOrchestrationService,
    ),
  ],
  logger: StackedLogger(),
)
/// The main application class.
class App {}
