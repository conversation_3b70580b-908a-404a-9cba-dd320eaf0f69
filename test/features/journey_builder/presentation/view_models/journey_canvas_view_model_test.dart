import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart'
    as models;
import 'package:parlant_flutter/features/journey_builder/domain/models/node_type.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/view_models/journey_canvas_view_model.dart';

class MockJourneyRepository extends Mock implements JourneyRepository {}

void main() {
  group('JourneyCanvasViewModel Tests -', () {
    late JourneyRepository mockJourneyRepository;

    setUp(() {
      locator.reset();
      mockJourneyRepository = MockJourneyRepository();
      locator.registerSingleton<JourneyRepository>(mockJourneyRepository);
      registerFallbackValue(
        const Journey(id: 'any', nodes: [], connections: []),
      );
    });

    tearDown(locator.reset);

    test(
      '''
When viewModel is initialised and no journey exists, 
it should create a new one
''',
      () async {
        when(mockJourneyRepository.getJourney).thenAnswer((_) async => null);
        when(
          () => mockJourneyRepository.saveJourney(any()),
        ).thenAnswer((_) async {});

        final model = JourneyCanvasViewModel();
        await model.initialise();

        expect(model.journey, isNotNull);
        expect(model.journey!.nodes, isEmpty);
        expect(model.journey!.connections, isEmpty);
        verify(mockJourneyRepository.getJourney).called(1);
        verify(() => mockJourneyRepository.saveJourney(any())).called(1);
      },
    );

    test(
      'When viewModel is initialised and a journey exists, it should load it',
      () async {
        const existingJourney = Journey(id: '1', nodes: [], connections: []);
        when(
          mockJourneyRepository.getJourney,
        ).thenAnswer((_) async => existingJourney);

        final model = JourneyCanvasViewModel();
        await model.initialise();

        expect(model.journey, existingJourney);
        verify(mockJourneyRepository.getJourney).called(1);
        verifyNever(() => mockJourneyRepository.saveJourney(any()));
      },
    );

    test(
      'When addNode is called, it should add a node to the journey and save it',
      () async {
        const initialJourney = Journey(id: '1', nodes: [], connections: []);
        when(
          mockJourneyRepository.getJourney,
        ).thenAnswer((_) async => initialJourney);
        when(
          () => mockJourneyRepository.saveJourney(any()),
        ).thenAnswer((_) async {});

        final model = JourneyCanvasViewModel();
        await model.initialise();

        var notified = false;
        model
          ..addListener(() {
            notified = true;
          })
          ..addNode(NodeType.start, const Offset(10, 20));

        expect(model.journey!.nodes.length, 1);
        expect(model.journey!.nodes.first, isA<models.StartNode>());
        expect(model.journey!.nodes.first.position, const Offset(10, 20));
        verify(() => mockJourneyRepository.saveJourney(any())).called(1);
        expect(notified, isTrue);
      },
    );
  });
}
