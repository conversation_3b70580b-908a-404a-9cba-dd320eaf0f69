import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/placeholder_engine_service.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/viewmodels/hello_world_viewmodel.dart';

class MockPlaceholderEngineService extends Mock
    implements PlaceholderEngineService {}

void main() {
  group('HelloWorldViewModel', () {
    late HelloWorldViewModel viewModel;
    late MockPlaceholderEngineService mockEngineService;

    setUp(() {
      mockEngineService = MockPlaceholderEngineService();
      locator.registerSingleton<PlaceholderEngineService>(mockEngineService);
      viewModel = HelloWorldViewModel();
    });

    tearDown(locator.reset);

    test('initial result should be "Press the button!"', () {
      expect(viewModel.result, 'Press the button!');
    });

    test('runProcess should call the engine service and update the result', () {
      const processedValue = 'Processed: Hello World';
      when(
        () => mockEngineService.processValue('Hello World'),
      ).thenReturn(processedValue);

      viewModel.runProcess();

      expect(viewModel.result, processedValue);
      verify(() => mockEngineService.processValue('Hello World')).called(1);
    });
  });
}
