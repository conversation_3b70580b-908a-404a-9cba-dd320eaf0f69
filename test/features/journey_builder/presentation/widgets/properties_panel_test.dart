import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/domain/repositories/journey_repository.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/journey_orchestration_service.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/widgets/properties_panel.dart';
import 'package:rxdart/rxdart.dart';

class MockJourneyOrchestrationService extends Mock
    implements JourneyOrchestrationService {}

class MockJourneyRepository extends Mock implements JourneyRepository {}

class FakeJourney extends Fake implements Journey {}

void main() {
  setUpAll(() {
    registerFallbackValue(FakeJourney());
  });
  group('PropertiesPanel', () {
    late JourneyOrchestrationService mockJourneyOrchestrationService;
    late JourneyRepository mockJourneyRepository;

    setUp(() {
      mockJourneyOrchestrationService = MockJourneyOrchestrationService();
      mockJourneyRepository = MockJourneyRepository();
      locator
        ..registerSingleton<JourneyOrchestrationService>(
          mockJourneyOrchestrationService,
        )
        ..registerSingleton<JourneyRepository>(mockJourneyRepository);
    });

    tearDown(locator.reset);

    testWidgets('shows "No node selected" when no node is selected', (
      WidgetTester tester,
    ) async {
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(null);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => Stream.value(null));

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('No node selected'), findsOneWidget);
    });

    testWidgets('shows properties for LLMNode', (WidgetTester tester) async {
      const node = LLMNode(
        id: '1',
        position: Offset.zero,
        prompt: 'Test Prompt',
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 1'), findsOneWidget);
      expect(find.text('Type: LLM'), findsOneWidget);
      expect(find.widgetWithText(TextFormField, 'Test Prompt'), findsOneWidget);
    });

    testWidgets('shows properties for MessageNode', (
      WidgetTester tester,
    ) async {
      const node = MessageNode(
        id: '2',
        position: Offset.zero,
        content: 'Test Content',
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 2'), findsOneWidget);
      expect(find.text('Type: Message'), findsOneWidget);
      expect(
        find.widgetWithText(TextFormField, 'Test Content'),
        findsOneWidget,
      );
    });

    testWidgets('updates prompt for LLMNode', (WidgetTester tester) async {
      const node = LLMNode(
        id: '1',
        position: Offset.zero,
        prompt: 'Test Prompt',
      );
      const journey = Journey(id: 'journey1', nodes: [node], connections: []);
      final subject = BehaviorSubject<Node?>.seeded(node);

      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);
      when(
        () => mockJourneyRepository.getJourney(),
      ).thenAnswer((_) async => journey);
      when(
        () => mockJourneyRepository.saveJourney(any()),
      ).thenAnswer((_) async {});
      when(() => mockJourneyOrchestrationService.selectNode(any())).thenAnswer((
        invocation,
      ) {
        subject.add(invocation.positionalArguments.first as Node);
      });

      await tester.pumpWidget(
        const MaterialApp(
          home: Directionality(
            textDirection: TextDirection.ltr,
            child: Scaffold(
              body: PropertiesPanel(),
            ),
          ),
        ),
      );

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Test Prompt'),
        'New Prompt',
      );
      await tester.pump();

      const expectedNode = LLMNode(
        id: '1',
        position: Offset.zero,
        prompt: 'New Prompt',
      );
      final expectedJourney = journey.copyWith(nodes: [expectedNode]);
      verify(
        () => mockJourneyRepository.saveJourney(expectedJourney),
      ).called(1);
      verify(
        () => mockJourneyOrchestrationService.selectNode(expectedNode),
      ).called(1);
    });

    testWidgets('shows properties for StartNode', (WidgetTester tester) async {
      const node = StartNode(
        id: '1',
        position: Offset.zero,
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 1'), findsOneWidget);
      expect(find.text('Type: Start'), findsOneWidget);
    });

    testWidgets('updates content for MessageNode', (WidgetTester tester) async {
      const node = MessageNode(
        id: '1',
        position: Offset.zero,
        content: 'Test Content',
      );
      const journey = Journey(id: 'journey1', nodes: [node], connections: []);
      final subject = BehaviorSubject<Node?>.seeded(node);

      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);
      when(
        () => mockJourneyRepository.getJourney(),
      ).thenAnswer((_) async => journey);
      when(
        () => mockJourneyRepository.saveJourney(any()),
      ).thenAnswer((_) async {});
      when(() => mockJourneyOrchestrationService.selectNode(any())).thenAnswer((
        invocation,
      ) {
        subject.add(invocation.positionalArguments.first as Node);
      });

      await tester.pumpWidget(
        const MaterialApp(
          home: Directionality(
            textDirection: TextDirection.ltr,
            child: Scaffold(
              body: PropertiesPanel(),
            ),
          ),
        ),
      );

      await tester.enterText(
        find.widgetWithText(TextFormField, 'Test Content'),
        'New Content',
      );
      await tester.pump();

      const expectedNode = MessageNode(
        id: '1',
        position: Offset.zero,
        content: 'New Content',
      );
      final expectedJourney = journey.copyWith(nodes: [expectedNode]);
      verify(
        () => mockJourneyRepository.saveJourney(expectedJourney),
      ).called(1);
      verify(
        () => mockJourneyOrchestrationService.selectNode(expectedNode),
      ).called(1);
    });

    testWidgets('shows properties for EndNode', (WidgetTester tester) async {
      const node = EndNode(
        id: '1',
        position: Offset.zero,
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 1'), findsOneWidget);
      expect(find.text('Type: End'), findsOneWidget);
    });

    testWidgets('shows properties for DecisionNode', (
      WidgetTester tester,
    ) async {
      const node = DecisionNode(
        id: '1',
        position: Offset.zero,
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 1'), findsOneWidget);
      expect(find.text('Type: Decision'), findsOneWidget);
    });

    testWidgets('shows properties for ToolNode', (WidgetTester tester) async {
      const node = ToolNode(
        id: '1',
        position: Offset.zero,
      );
      final subject = BehaviorSubject<Node?>.seeded(node);
      when(() => mockJourneyOrchestrationService.selectedNode).thenReturn(node);
      when(
        () => mockJourneyOrchestrationService.selectedNodeStream,
      ).thenAnswer((_) => subject.stream);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PropertiesPanel(),
          ),
        ),
      );

      expect(find.text('ID: 1'), findsOneWidget);
      expect(find.text('Type: Tool'), findsOneWidget);
    });
  });
}
