import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/app/app.locator.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/placeholder_engine_service.dart';
import 'package:parlant_flutter/features/journey_builder/presentation/views/hello_world_view.dart';

class MockPlaceholderEngineService extends Mock
    implements PlaceholderEngineService {}

void main() {
  late PlaceholderEngineService mockEngineService;

  setUp(() {
    mockEngineService = MockPlaceholderEngineService();
    locator.registerSingleton<PlaceholderEngineService>(mockEngineService);
  });

  tearDown(locator.reset);

  testWidgets('HelloWorldView should be created', (tester) async {
    const key = Key('hello-world-view');
    await tester.pumpWidget(const MaterialApp(home: HelloWorldView(key: key)));
    expect(find.by<PERSON>ey(key), findsOneWidget);
  });

  testWidgets('should have an app bar with the correct title', (tester) async {
    await tester.pumpWidget(const MaterialApp(home: HelloWorldView()));
    expect(find.widgetWithText(AppBar, 'Hello World'), findsOneWidget);
  });

  testWidgets('should have a scaffold', (tester) async {
    await tester.pumpWidget(const MaterialApp(home: HelloWorldView()));
    expect(find.byType(Scaffold), findsOneWidget);
  });

  testWidgets(
    'HelloWorldView - when button is tapped, should show processed text',
    (tester) async {
      // Arrange
      when(
        () => mockEngineService.processValue(any()),
      ).thenReturn('mocked-processed');

      await tester.pumpWidget(const MaterialApp(home: HelloWorldView()));

      // Assert initial state
      expect(find.text('Press the button!'), findsOneWidget);

      // Act
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert final state
      expect(find.text('mocked-processed'), findsOneWidget);
    },
  );
}
