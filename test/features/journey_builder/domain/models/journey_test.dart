import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/connection.dart'
    as app;
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';

void main() {
  group('Journey', () {
    test('from<PERSON><PERSON> and to<PERSON><PERSON> should work correctly', () {
      const journey = Journey(
        id: '1',
        nodes: [
          StartNode(
            id: '1',
            position: Offset.zero,
          ),
        ],
        connections: [
          app.Connection(
            id: '1',
            fromNodeId: '1',
            toNodeId: '2',
            fromPortId: 'out',
            toPortId: 'in',
          ),
        ],
      );
      final json = journey.toJson();
      final newJourney = Journey.fromJson(json);
      expect(newJourney, journey);
    });

    test('copyWith should work correctly', () {
      const journey = Journey(
        id: '1',
        nodes: [
          StartNode(
            id: '1',
            position: Offset.zero,
          ),
        ],
        connections: [
          app.Connection(
            id: '1',
            fromNodeId: '1',
            toNodeId: '2',
            fromPortId: 'out',
            toPortId: 'in',
          ),
        ],
      );
      final newJourney = journey.copyWith(id: '2');
      expect(newJourney.id, '2');
      expect(newJourney.nodes, journey.nodes);
      expect(newJourney.connections, journey.connections);
    });
  });
}
