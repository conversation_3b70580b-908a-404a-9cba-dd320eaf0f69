import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';

void main() {
  group('Node', () {
    test('from<PERSON><PERSON> and to<PERSON>son should work correctly for StartNode', () {
      const node = StartNode(
        id: '1',
        position: Offset.zero,
      );
      final json = node.toJson();
      final newNode = Node.fromJson(json);
      expect(newNode, node);
    });

    test('copyWith should work correctly for StartNode', () {
      const node = StartNode(
        id: '1',
        position: Offset.zero,
      );
      final newNode = node.copyWith(id: '2');
      expect(newNode.id, '2');
      expect(newNode.position, Offset.zero);
    });
  });
}
