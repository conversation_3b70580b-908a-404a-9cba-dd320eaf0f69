import 'package:flutter_test/flutter_test.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/node.dart';
import 'package:parlant_flutter/features/journey_builder/domain/services/journey_orchestration_service.dart';

void main() {
  late JourneyOrchestrationService journeyOrchestrationService;

  group('JourneyOrchestrationService', () {
    setUp(() {
      journeyOrchestrationService = JourneyOrchestrationService();
    });

    test('initial selectedNode should be null', () {
      expect(journeyOrchestrationService.selectedNode, isNull);
    });

    test('selectNode should update the selectedNode', () {
      const node = StartNode(id: '1', position: Offset.zero);
      journeyOrchestrationService.selectNode(node);
      expect(journeyOrchestrationService.selectedNode, equals(node));
    });

    test('selectedNodeStream should emit the selected node', () {
      const node = StartNode(id: '1', position: Offset.zero);
      expect(
        journeyOrchestrationService.selectedNodeStream,
        emitsInOrder([null, node]),
      );
      journeyOrchestrationService
        ..selectNode(node)
        ..dispose();
    });
  });
}
