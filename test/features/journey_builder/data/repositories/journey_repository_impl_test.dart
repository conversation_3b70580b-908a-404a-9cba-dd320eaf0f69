import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/features/journey_builder/data/repositories/journey_repository_impl.dart';
import 'package:parlant_flutter/features/journey_builder/domain/models/journey.dart';
import 'package:surrealdb/surrealdb.dart';

class MockSurrealDB extends Mock implements SurrealDB {}

void main() {
  late JourneyRepositoryImpl journeyRepository;
  late MockSurrealDB mockSurrealDB;

  setUp(() {
    mockSurrealDB = MockSurrealDB();
    journeyRepository = JourneyRepositoryImpl(db: mockSurrealDB);
  });

  group('JourneyRepositoryImpl', () {
    const journey = Journey(
      id: '1',
      nodes: [],
      connections: [],
    );

    test(
      'getJourney should return a journey when the database is not empty',
      () async {
        when(() => mockSurrealDB.connect()).thenAnswer((_) async {});
        when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
        when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
        when(
          () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
        ).thenAnswer((_) async => [journey.toJson()]);

        final result = await journeyRepository.getJourney();

        expect(result, equals(journey));
        verify(
          () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
        ).called(1);
      },
    );

    test('getJourney should return null when the database is empty', () async {
      when(() => mockSurrealDB.connect()).thenAnswer((_) async {});
      when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
      when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
      when(
        () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
      ).thenAnswer((_) async => [{}]);

      final result = await journeyRepository.getJourney();

      expect(result, isNull);
      verify(
        () => mockSurrealDB.select<Map<String, dynamic>>('journey'),
      ).called(1);
    });

    test('saveJourney should call the update method on the database', () async {
      when(() => mockSurrealDB.connect()).thenAnswer((_) async {});
      when(() => mockSurrealDB.wait()).thenAnswer((_) async {});
      when(() => mockSurrealDB.use('test', 'test')).thenAnswer((_) async {});
      when(
        () => mockSurrealDB.update('journey', journey.toJson()),
      ).thenAnswer((_) async => [journey.toJson()]);

      await journeyRepository.saveJourney(journey);

      verify(() => mockSurrealDB.update('journey', journey.toJson())).called(1);
    });
  });
}
