import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_view.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_viewmodel.dart';

class MockSettingsViewModel extends Mock implements SettingsViewModel {}

class MockNavigatorObserver extends Mock implements NavigatorObserver {}

void main() {
  late SettingsViewModel mockSettingsViewModel;

  setUp(() {
    mockSettingsViewModel = MockSettingsViewModel();
    when(
      () => mockSettingsViewModel.apiKeyController,
    ).thenReturn(TextEditingController());
    when(() => mockSettingsViewModel.saveApiKey()).thenAnswer((_) async {});
    registerFallbackValue(
      MaterialPageRoute<void>(builder: (context) => Container()),
    );
  });

  Widget buildTestableWidget({NavigatorObserver? observer}) {
    return MaterialApp(
      home: SettingsView(
        viewModel: mockSettingsViewModel,
      ),
      navigatorObservers: observer != null ? [observer] : [],
    );
  }

  testWidgets('SettingsView should be created', (tester) async {
    await tester.pumpWidget(buildTestableWidget());
    expect(find.byType(SettingsView), findsOneWidget);
  });

  testWidgets('should have an app bar with the correct title', (tester) async {
    await tester.pumpWidget(buildTestableWidget());
    expect(find.widgetWithText(AppBar, 'Settings'), findsOneWidget);
  });

  testWidgets('should have a text field for the API key', (tester) async {
    await tester.pumpWidget(buildTestableWidget());
    expect(find.byKey(const Key('apiKeyField')), findsOneWidget);
  });

  testWidgets('should have a save button', (tester) async {
    await tester.pumpWidget(buildTestableWidget());
    expect(find.byKey(const Key('saveApiKeyButton')), findsOneWidget);
  });

  testWidgets(
    'tapping the save button should call saveApiKey on the view model',
    (tester) async {
      await tester.pumpWidget(buildTestableWidget());
      await tester.tap(find.byKey(const Key('saveApiKeyButton')));
      verify(() => mockSettingsViewModel.saveApiKey()).called(1);
    },
  );

  testWidgets('back button should have a pop callback', (tester) async {
    await tester.pumpWidget(buildTestableWidget());
    final backButton = tester.widget<IconButton>(
      find.byKey(const Key('backButton')),
    );
    expect(backButton.onPressed, isNotNull);
  });

  testWidgets('tapping the back button should pop the navigator', (
    tester,
  ) async {
    final mockObserver = MockNavigatorObserver();
    await tester.pumpWidget(buildTestableWidget(observer: mockObserver));

    await tester.tap(find.byKey(const Key('backButton')));
    await tester.pumpAndSettle();

    verify(() => mockObserver.didPop(any(), any())).called(1);
  });
}
