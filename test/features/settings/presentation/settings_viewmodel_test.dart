import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:parlant_flutter/features/settings/presentation/settings_viewmodel.dart';

class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

void main() {
  late SettingsViewModel settingsViewModel;
  late MockFlutterSecureStorage mockSecureStorage;

  setUp(() {
    mockSecureStorage = MockFlutterSecureStorage();
    settingsViewModel = SettingsViewModel(secureStorage: mockSecureStorage);
  });

  group('SettingsViewModel', () {
    test('saveApi<PERSON>ey should call secureStorage.write', () async {
      // Arrange
      when(
        () => mockSecureStorage.write(key: 'llm_api_key', value: 'test_key'),
      ).thenAnswer((_) async {});
      settingsViewModel.apiKeyController.text = 'test_key';

      // Act
      await settingsViewModel.saveApiKey();

      // Assert
      verify(
        () => mockSecureStorage.write(key: 'llm_api_key', value: 'test_key'),
      ).called(1);
    });
  });
}
